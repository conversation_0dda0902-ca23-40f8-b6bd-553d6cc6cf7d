from poe_api_wrapper import PoeApi
import asyncio
tokens = {
    'p-b': "1bMcswpdGKuWe2-zxIjkZQ%3D%3D", 
    'p-lat': "raPJyYB78WPHN1rG38ZEN2pqsf2h8wjS2M%2BJ4Cceig%3D%3D",
}

def main():
    client = PoeApi(tokens=tokens)
    message = "Explain quantum computing in simple terms"
    for chunk in client.send_message(bot="claude_3_igloo", message=message):
        print(chunk["response"], end='', flush=True)
        
main()
