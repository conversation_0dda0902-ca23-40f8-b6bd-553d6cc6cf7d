from flask import Flask, request, jsonify, Response
from poe_api_wrapper import Poe<PERSON>pi
import asyncio

app = Flask(__name__)

# Retrieve tokens from environment variables
p_b_token = os.environ.get('POE_P_B_TOKEN')
p_lat_token = os.environ.get('POE_P_LAT_TOKEN')

tokens = {
    'p-b': p_b_token, 
    'p-lat': p_lat_token,
}

client = PoeApi(tokens=tokens)

@app.route('/chat', methods=['POST'])
def chat():
    data = request.json
    message = data.get('message')
    bot = data.get('bot', 'claude_3_igloo')

    if not message:
        return jsonify({"error": "Message is required"}), 400

    def generate():
        for chunk in client.send_message(bot=bot, message=message):
            yield chunk["response"]

    return Response(generate(), mimetype='text/plain')

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy"}), 200

if __name__ == '__main__':
    app.run(debug=True)
