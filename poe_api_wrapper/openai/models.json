{"gpt-3.5-turbo": {"baseModel": "gpt3_5", "tokens": 2000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "openai"}, "gpt-3.5-turbo-instruct": {"baseModel": "chinchilla_instruct", "tokens": 2000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "openai"}, "gpt-3.5-turbo-16k": {"baseModel": "agouti", "tokens": 16000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "openai"}, "gpt-4": {"baseModel": "gpt4_classic", "tokens": 2000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "openai"}, "gpt-4-turbo": {"baseModel": "beaver", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "openai"}, "gpt-4-turbo-128k": {"baseModel": "vizcacha", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "openai"}, "gpt-4o": {"baseModel": "gpt4_o", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "openai"}, "gpt-4o-128k": {"baseModel": "gpt4_o_128k", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "openai"}, "gpt-4o-mini": {"baseModel": "gpt4_o_mini", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "openai"}, "gpt-4o-mini-128k": {"baseModel": "gpt4_o_mini_128k", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "openai"}, "claude-instant": {"baseModel": "a2", "tokens": 9000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "anthropic"}, "claude-instant-100k": {"baseModel": "a2_100k", "tokens": 100000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "anthropic"}, "claude-2": {"baseModel": "claude_2_short", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "anthropic"}, "claude-2-100k": {"baseModel": "a2_2", "tokens": 100000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "anthropic"}, "claude-3-opus": {"baseModel": "claude_2_1_cedar", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "anthropic"}, "claude-3-sonnet": {"baseModel": "claude_2_1_bamboo", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "anthropic"}, "claude-3-haiku": {"baseModel": "claude_3_haiku", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "anthropic"}, "claude-3-opus-200k": {"baseModel": "claude_3_opus_200k", "tokens": 200000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "anthropic"}, "claude-3-sonnet-200k": {"baseModel": "claude_3_sonnet_200k", "tokens": 200000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "anthropic"}, "claude-3-haiku-200k": {"baseModel": "claude_3_haiku_200k", "tokens": 200000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "anthropic"}, "claude-3.5-sonnet": {"baseModel": "claude_3_igloo", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "anthropic"}, "claude-3.5-sonnet-200k": {"baseModel": "claude_3_igloo_200k", "tokens": 200000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "anthropic"}, "gemini-1.5-pro": {"baseModel": "gemini_1_5_pro", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "google"}, "gemini-1.5-pro-128k": {"baseModel": "gemini_1_5_pro_128k", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "google"}, "gemini-1.5-pro-2m": {"baseModel": "gemini_1_5_pro_1m", "tokens": 2000000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "google"}, "gemini-1.5-flash": {"baseModel": "gemini_1_5_flash", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "google"}, "gemini-1.5-flash-128k": {"baseModel": "gemini_1_5_flash_128k", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "google"}, "gemini-1.5-flash-1m": {"baseModel": "gemini_1_5_flash_1m", "tokens": 1000000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "google"}, "gemini-1.5-flash-search": {"baseModel": "gemini_pro_search", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "google"}, "gemini-1.5-pro-search": {"baseModel": "gemini_1_5_pro_search", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": true, "object": "model", "owned_by": "google"}, "code-llama-13b": {"baseModel": "code_llama_13b_instruct", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "code-llama-34b": {"baseModel": "code_llama_34b_instruct", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "llama-3-8b": {"baseModel": "groq_llama_3_8b", "tokens": 8000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "llama-3-70b": {"baseModel": "groq_llama_3_70b", "tokens": 8000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "llama-3-70b-instruct": {"baseModel": "llama370bt", "tokens": 8000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "llama-3.1-8b-128k": {"baseModel": "llama318bfw128k", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "llama-3.1-70b-128k": {"baseModel": "llama3170bfw128k", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "llama-3.1-405b-instruct": {"baseModel": "llama31405bt", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "llama-3.1-405b-128k": {"baseModel": "llama31405bfw128k", "tokens": 128000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "meta"}, "mistral-7b": {"baseModel": "groq_mixtral_8x_7b", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "mist<PERSON><PERSON>"}, "mistral-medium": {"baseModel": "mistral_medium", "tokens": 32000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "mist<PERSON><PERSON>"}, "mistral-large": {"baseModel": "mistral_large", "tokens": 32000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "mist<PERSON><PERSON>"}, "palm-2": {"baseModel": "acouchy", "tokens": 8000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "google"}, "mytho-max-l2-13b": {"baseModel": "mythomaxl213b", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "gryphe"}, "solar-mini": {"baseModel": "upstage_solar_0_70b_16bit", "tokens": 2000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "upstage"}, "gemma-7b": {"baseModel": "gemma7bfw", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "google"}, "gemma-instruct-7b": {"baseModel": "gemmainstruct7bt", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "google"}, "qwen-1.5-72b": {"baseModel": "qwen72bt", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "alibaba"}, "qwen-1.5-110b": {"baseModel": "qwen15110bt", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "alibaba"}, "qwen-2-72b": {"baseModel": "qwen272bchat", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "alibaba"}, "qwen-2-72b-instruct": {"baseModel": "qwen272binstructt", "tokens": 4000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "alibaba"}, "snowflake-arctic-instruct": {"baseModel": "snowflakearctict", "tokens": 2000, "endpoints": ["/v1/chat/completions"], "premium_model": false, "object": "model", "owned_by": "snowflake"}, "playground-v2.5": {"baseModel": "playgroundv25", "tokens": 80, "endpoints": ["/v1/images/generations", "/v1/images/edits"], "premium_model": false, "object": "model", "owned_by": "playgroundai", "sizes": {"1024x1024": "--aspect 1:1", "1254x836": "--aspect 3:2", "836x1254": "--aspect 2:3", "1182x886": "--aspect 4:3", "886x1182": "--aspect 3:4", "1161x903": "--aspect 9:7", "903x1161": "--aspect 7:9", "1536x640": "--aspect 12:5", "640x1536": "--aspect 5:12", "1365x768": "--aspect 16:9", "768x1365": "--aspect 9:16", "1295x809": "--aspect 16:10", "809x1295": "--aspect 10:16"}}, "sdxl": {"baseModel": "stablediffusionxl", "tokens": 80, "endpoints": ["/v1/images/generations", "/v1/images/edits"], "premium_model": false, "object": "model", "owned_by": "stability<PERSON>", "sizes": {"1024x1024": "--aspect 1:1", "1792x1024": "--aspect 7:4", "1024x1792": "--aspect 4:7", "1152x896": "--aspect 9:7", "896x1152": "--aspect 7:9", "1216x832": "--aspect 19:13", "832x1216": "--aspect 13:19", "1536x640": "--aspect 12:5", "640x1536": "--aspect 5:12"}}, "sdxl-lightning": {"baseModel": "falsdxllightning", "tokens": 80, "endpoints": ["/v1/images/generations"], "premium_model": false, "object": "model", "owned_by": "bytedance"}, "stable-diffusion-3": {"baseModel": "stablediffusion3", "tokens": 80, "endpoints": ["/v1/images/generations", "/v1/images/edits"], "premium_model": true, "object": "model", "owned_by": "stability<PERSON>"}, "stable-diffusion-3-turbo": {"baseModel": "sd3turbo", "tokens": 80, "endpoints": ["/v1/images/generations", "/v1/images/edits"], "premium_model": true, "object": "model", "owned_by": "stability<PERSON>"}, "stable-diffusion-3-2b": {"baseModel": "stablediffusion32b", "tokens": 80, "endpoints": ["/v1/images/generations", "/v1/images/edits"], "premium_model": false, "object": "model", "owned_by": "stability<PERSON>"}, "dall-e-3": {"baseModel": "dalle3", "tokens": 80, "endpoints": ["/v1/images/generations", "/v1/images/edits"], "premium_model": true, "object": "model", "owned_by": "openai", "sizes": {"1024x1024": "--aspect 1:1", "1792x1024": "--aspect 7:4", "1024x1792": "--aspect 4:7"}}, "ideogram": {"baseModel": "ideogram", "tokens": 80, "endpoints": ["/v1/images/generations", "/v1/images/edits"], "premium_model": true, "object": "model", "owned_by": "ideogram", "sizes": {"1024x1024": "--aspect 1:1", "1254x836": "--aspect 3:2", "836x1254": "--aspect 2:3", "1182x886": "--aspect 4:3", "886x1182": "--aspect 3:4", "1365x768": "--aspect 16:9", "768x1365": "--aspect 9:16", "1295x809": "--aspect 16:10", "809x1295": "--aspect 10:16"}}}